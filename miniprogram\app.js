// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 自动登录获取openid
    this.autoLogin()
  },

  globalData: {
    userInfo: null,
    openid: null
  },

  /**
   * 自动登录获取openid
   */
  autoLogin() {
    console.log('开始自动登录获取openid')
    
    // 检查是否已有openid
    const storedOpenid = wx.getStorageSync('openid')
    if (storedOpenid) {
      console.log('使用缓存的openid:', storedOpenid)
      this.globalData.openid = storedOpenid
      return Promise.resolve(storedOpenid)
    }

    // 调用微信登录
    return new Promise((resolve, reject) => {
      wx.login({
        success: (loginRes) => {
          if (loginRes.code) {
            console.log('微信登录成功，code:', loginRes.code)
            
            // 调用后端API获取openid
            this.getOpenidFromBackend(loginRes.code)
              .then((openid) => {
                console.log('获取openid成功:', openid)
                this.globalData.openid = openid
                wx.setStorageSync('openid', openid)
                resolve(openid)
              })
              .catch((error) => {
                console.error('获取openid失败:', error)
                reject(error)
              })
          } else {
            console.error('微信登录失败:', loginRes.errMsg)
            reject(new Error('微信登录失败'))
          }
        },
        fail: (error) => {
          console.error('微信登录调用失败:', error)
          reject(error)
        }
      })
    })
  },

  /**
   * 从后端获取openid
   * @param {string} code 微信登录code
   */
  getOpenidFromBackend(code) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'http://localhost:8080/auth/wechat-login', // 修正后端登录接口路径
        method: 'POST',
        data: {
          code: code
        },
        header: {
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('后端登录接口响应:', res)
          
          if (res.statusCode === 200 && res.data.code === 200) {
            // 从登录响应中获取用户信息，然后提取openid
            const loginData = res.data.data
            let openid = null
            
            // 如果返回的是用户信息对象，从中提取openid
            if (loginData && loginData.user && loginData.user.wechatOpenid) {
              openid = loginData.user.wechatOpenid
            } else if (loginData && loginData.wechatOpenid) {
              openid = loginData.wechatOpenid
            } else if (loginData && loginData.openid) {
              openid = loginData.openid
            }
            
            if (openid) {
              resolve(openid)
            } else {
              reject(new Error('后端返回的openid为空'))
            }
          } else {
            reject(new Error(res.data.message || '后端登录接口调用失败'))
          }
        },
        fail: (error) => {
          console.error('调用后端登录接口失败:', error)
          reject(error)
        }
      })
    })
  },

  /**
   * 获取openid（供其他页面调用）
   */
  getOpenid() {
    // 如果全局数据中有openid，直接返回
    if (this.globalData.openid) {
      return Promise.resolve(this.globalData.openid)
    }

    // 如果本地存储中有openid，使用并更新全局数据
    const storedOpenid = wx.getStorageSync('openid')
    if (storedOpenid) {
      this.globalData.openid = storedOpenid
      return Promise.resolve(storedOpenid)
    }

    // 否则重新登录获取
    return this.autoLogin()
  }
})
