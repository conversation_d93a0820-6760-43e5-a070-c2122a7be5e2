package com.cloudpasture.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudpasture.common.Result;
import com.cloudpasture.dto.PaymentRequestDTO;
import com.cloudpasture.dto.RefundRequestDTO;
import com.cloudpasture.dto.WechatPayNotifyDTO;
import com.cloudpasture.entity.PaymentOrder;
import com.cloudpasture.service.AuthService;
import com.cloudpasture.service.PaymentService;
import com.cloudpasture.utils.WechatPayUtils;
import com.cloudpasture.vo.PaymentResponseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 支付控制器
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Slf4j
@RestController
@RequestMapping("/payment")
@Api(tags = "支付管理")
public class PaymentController {

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private AuthService authService;

    @Autowired
    private WechatPayUtils wechatPayUtils;

    @PostMapping("/create")
    @ApiOperation("创建支付订单")
    public Result<PaymentResponseVO> createPayment(
            @RequestHeader("Authorization") String token,
            @RequestBody @Valid PaymentRequestDTO paymentRequest) {
        try {
            // 移除Bearer前缀
            if (token.startsWith("Bearer ")) {
                token = token.substring(7);
            }
            Long userId = authService.getUserIdFromToken(token);

            // 验证请求参数
            if (paymentRequest.getBusinessOrderNo() == null || paymentRequest.getTotalAmount() == null) {
                return Result.error(400, "业务订单号和支付金额不能为空");
            }

            // 验证微信支付必需的openid参数
            if (paymentRequest.getPaymentMethod() == 1 && 
                (paymentRequest.getOpenid() == null || paymentRequest.getOpenid().trim().isEmpty())) {
                return Result.error(400, "微信支付需要提供有效的openid");
            }

            PaymentResponseVO response = paymentService.createPayment(userId, paymentRequest);
            return Result.success(response);

        } catch (Exception e) {
            log.error("创建支付订单异常: ", e);
            return Result.error(500, "创建支付订单失败：" + e.getMessage());
        }
    }

    @GetMapping("/order/{paymentOrderNo}")
    @ApiOperation("查询支付订单")
    public Result<PaymentOrder> getPaymentOrder(
            @PathVariable String paymentOrderNo) {
        PaymentOrder paymentOrder = paymentService.getPaymentOrder(paymentOrderNo);
        return Result.success(paymentOrder);
    }

    @GetMapping("/orders")
    @ApiOperation("查询用户支付订单列表")
    public Result<IPage<PaymentOrder>> getUserPaymentOrders(
            @RequestHeader("Authorization") String token,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer current,
            @ApiParam("每页数量") @RequestParam(defaultValue = "10") Integer size) {
        // 移除Bearer前缀
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        Long userId = authService.getUserIdFromToken(token);
        Page<PaymentOrder> page = new Page<>(current, size);
        IPage<PaymentOrder> paymentOrders = paymentService.getUserPaymentOrders(userId, page);
        return Result.success(paymentOrders);
    }

    @GetMapping("/status/{paymentOrderNo}")
    @ApiOperation("查询支付状态")
    public Result<Integer> queryPaymentStatus(
            @PathVariable String paymentOrderNo) {
        Integer status = paymentService.queryPaymentStatus(paymentOrderNo);
        return Result.success(status);
    }

    @PostMapping("/refund")
    @ApiOperation("申请退款")
    public Result<Boolean> refundPayment(
            @RequestHeader("Authorization") String token,
            @RequestBody @Valid RefundRequestDTO refundRequest) {
        // 移除Bearer前缀
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        Long userId = authService.getUserIdFromToken(token);
        boolean success = paymentService.refundPayment(userId, refundRequest);
        return Result.success(success);
    }

    @PostMapping("/cancel/{paymentOrderNo}")
    @ApiOperation("取消支付订单")
    public Result<Boolean> cancelPayment(
            @PathVariable String paymentOrderNo) {
        boolean success = paymentService.cancelPayment(paymentOrderNo);
        return Result.success(success);
    }

    @PostMapping("/wechat/notify")
    @ApiOperation("微信支付回调通知")
    public String wechatPayNotify(
            @RequestBody String requestBody,
            HttpServletRequest request) {
        try {
            log.info("收到微信支付回调通知：{}", requestBody);

            // 获取请求头信息用于验签
            String timestamp = request.getHeader("Wechatpay-Timestamp");
            String nonce = request.getHeader("Wechatpay-Nonce");
            String signature = request.getHeader("Wechatpay-Signature");
            String serial = request.getHeader("Wechatpay-Serial");

            log.info("微信支付回调头信息 - Timestamp: {}, Nonce: {}, Signature: {}, Serial: {}", 
                timestamp, nonce, signature, serial);

            // 验证签名（这里简化处理，实际使用时需要严格验签）
            // 验证必要的头信息
            if (timestamp == null || nonce == null || signature == null) {
                log.error("微信支付回调缺少必要的头信息");
                return "{\"code\":\"FAIL\",\"message\":\"缺少必要的头信息\"}";
            }

            // 验证签名
            boolean signatureValid = wechatPayUtils.verifyNotifySignature(timestamp, nonce, requestBody, signature);
            if (!signatureValid) {
                log.error("微信支付回调签名验证失败 - Timestamp: {}, Nonce: {}, Signature: {}", 
                    timestamp, nonce, signature);
                log.error("回调请求体: {}", requestBody);
                return "{\"code\":\"FAIL\",\"message\":\"签名验证失败\"}";
            }
            
            log.info("微信支付回调签名验证成功");

            // 解析回调数据
            WechatPayNotifyDTO notifyData = com.alibaba.fastjson.JSON.parseObject(requestBody, WechatPayNotifyDTO.class);
            
            // 处理支付回调
            boolean success = paymentService.handleWechatPayNotify(notifyData);
            
            if (success) {
                log.info("微信支付回调处理成功");
                return "{\"code\":\"SUCCESS\",\"message\":\"成功\"}";
            } else {
                log.error("微信支付回调处理失败");
                return "{\"code\":\"FAIL\",\"message\":\"处理失败\"}";
            }

        } catch (Exception e) {
            log.error("微信支付回调处理异常：", e);
            return "{\"code\":\"FAIL\",\"message\":\"处理异常\"}";
        }
    }
}
