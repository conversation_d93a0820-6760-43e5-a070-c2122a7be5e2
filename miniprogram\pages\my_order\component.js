// 引入API配置
const { request, API_ENDPOINTS, API } = require('../../utils/api.js');
// 引入订单操作工具
const { cancelOrder, payOrderNow, canCancelOrder, canPayOrder } = require('../../utils/orderHelper.js');
// 引入支付辅助工具
const { createPaymentAndPay } = require('../../utils/paymentHelper.js');

Component({
  properties: {},
  data: {
    // ========== 订单数据区域 ==========
    orderList: [], // 订单数据列表，将在页面加载时填充
    filteredOrders: [], // 筛选后的订单列表
    currentTab: 'all', // 当前选中的标签页：all-全部, pending-待付款, shipped-待收货, completed-已完成, cancelled-已取消

    // 订单状态映射（后端数字状态 -> 文本）
    statusMap: {
      0: '已取消',
      1: '待支付',
      2: '已支付',
      3: '已发货',
      4: '已完成',
      5: '已取消'
    },


    // ========== 订单数据区域结束 ==========
  },

  lifetimes: {
    created: function () {},
    attached: function () {
      console.info("我的订单页面加载");
      console.log("初始数据:", this.data);
      this.loadOrderData();
    },
    detached: function () {
      console.info("我的订单页面卸载");
    },
  },

  methods: {
    // ========== API预留接口区域 ==========
    /**
     * 加载订单数据 - 从后端API获取
     * @param {number} current 页码
     * @param {number} size 每页数量
     */
    loadOrderData: function(current = 1, size = 10) {
      console.log('开始加载订单数据:', { current, size });

      // 设置加载状态
      this.setData({
        loading: true
      });

      API.order.getList(current, size)
        .then(async (data) => {
          console.log('订单API响应:', data);

          if (data.code === 200) {
            const orders = data.data.records || [];

            // 为每个订单获取详细信息
            const enrichedOrders = await Promise.all(orders.map(async (order) => {
              const enrichedOrder = {
                id: order.id,
                orderType: order.orderType,
                orderStatus: order.orderStatus,
                statusText: this.getOrderStatusText(order.orderStatus),
                totalAmount: order.totalAmount,
                subtotalAmount: order.subtotalAmount,
                shippingFee: order.shippingFee,
                pointsDiscount: order.pointsDiscount,
                pointsUsed: order.pointsUsed,
                createTime: this.formatTime(order.createTime),
                items: []
              };

              // 获取订单商品明细
              try {
                const itemsResponse = await API.order.getItems(order.id);
                if (itemsResponse.code === 200 && itemsResponse.data) {
                  // 为每个商品获取详细信息
                  const enrichedItems = await Promise.all(itemsResponse.data.map(async (item) => {
                    const enrichedItem = { ...item };

                    // 获取商品信息
                    if (item.productId) {
                      try {
                        const productResponse = await API.product.getById(item.productId);
                        if (productResponse.code === 200 && productResponse.data) {
                          enrichedItem.productInfo = productResponse.data;

                          // 处理商品轮播图
                          enrichedItem.productImageUrl = this.getProductImageUrl(productResponse.data.productCarousel);
                        }
                      } catch (error) {
                        console.warn(`获取商品${item.productId}信息失败:`, error);
                      }
                    }

                    // 获取规格信息
                    if (item.productSpecificationId) {
                      try {
                        const specResponse = await API.productSpecification.getById(item.productSpecificationId);
                        if (specResponse.code === 200 && specResponse.data) {
                          enrichedItem.specificationInfo = specResponse.data;
                        }
                      } catch (error) {
                        console.warn(`获取规格${item.productSpecificationId}信息失败:`, error);
                      }
                    }

                    return enrichedItem;
                  }));

                  enrichedOrder.items = enrichedItems;
                  enrichedOrder.itemCount = enrichedItems.length;
                }
              } catch (error) {
                console.warn(`获取订单${order.id}商品明细失败:`, error);
              }

              return enrichedOrder;
            }));

            this.setData({
              orderList: enrichedOrders,
              filteredOrders: enrichedOrders,
              loading: false
            });

            console.log('订单数据加载完成:', enrichedOrders.length);
          } else {
            throw new Error(data.message || '获取订单数据失败');
          }
        })
        .catch((error) => {
          console.error('加载订单数据失败:', error);

          // 设置空状态
          this.setData({
            orderList: [],
            filteredOrders: [],
            loading: false
          });

          wx.showToast({
            title: '数据加载失败',
            icon: 'none'
          });
        });
    },

    /**
     * 获取订单状态文本
     * @param {number} status 订单状态
     * @returns {string} 状态文本
     */
    getOrderStatusText: function(status) {
      return this.data.statusMap[status] || '未知状态';
    },

    /**
     * 格式化时间
     * @param {string} timeString 时间字符串
     * @returns {string} 格式化后的时间
     */
    formatTime: function(timeString) {
      if (!timeString) return '';

      try {
        // 如果是标准时间格式，直接截取前16位
        if (typeof timeString === 'string' && timeString.length >= 16) {
          return timeString.substring(0, 16).replace('T', ' ');
        }

        // 如果是其他格式，尝试转换
        const date = new Date(timeString);
        if (!isNaN(date.getTime())) {
          return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          });
        }

        return timeString;
      } catch (error) {
        console.warn('时间格式化失败:', timeString, error);
        return timeString || '';
      }
    },

    /**
     * 获取商品图片URL
     * @param {string} productCarousel 商品轮播图JSON字符串
     * @returns {string} 图片URL
     */
    getProductImageUrl: function(productCarousel) {
      if (!productCarousel) {
        return '/images/default-product.png';
      }

      try {
        // 如果是JSON字符串，解析为数组
        if (typeof productCarousel === 'string') {
          const carouselArray = JSON.parse(productCarousel);
          if (Array.isArray(carouselArray) && carouselArray.length > 0) {
            return carouselArray[0];
          }
        }

        // 如果已经是数组
        if (Array.isArray(productCarousel) && productCarousel.length > 0) {
          return productCarousel[0];
        }

        // 如果是单个字符串URL
        if (typeof productCarousel === 'string' && productCarousel.trim()) {
          return productCarousel;
        }

        return '/images/default-product.png';
      } catch (error) {
        console.warn('解析商品轮播图失败:', productCarousel, error);
        return '/images/default-product.png';
      }
    },

    /**
     * 更新订单状态 - API调用预留接口
     * @param {string} orderId 订单ID
     * @param {string} newStatus 新状态
     * TODO: 替换为真实API调用
     */
    updateOrderStatus: function(orderId, newStatus) {
      try {
        // TODO: 替换为真实API调用
        // await wx.request({
        //   url: `https://api.example.com/orders/${orderId}/status`,
        //   method: 'PUT',
        //   data: { status: newStatus },
        //   header: {
        //     'Authorization': 'Bearer ' + wx.getStorageSync('token')
        //   }
        // });

        // 临时更新本地数据
        const orderList = this.data.orderList.map(order => {
          if (order.id === orderId) {
            const updatedOrder = { ...order, status: newStatus };

            // 根据状态更新时间戳
            const currentTime = new Date().toLocaleString();
            switch (newStatus) {
              case 'paid':
                updatedOrder.payTime = currentTime;
                break;
              case 'shipped':
                updatedOrder.shipTime = currentTime;
                break;
              case 'delivered':
                updatedOrder.deliverTime = currentTime;
                break;
              case 'completed':
                updatedOrder.completeTime = currentTime;
                break;
              case 'cancelled':
                updatedOrder.cancelTime = currentTime;
                break;
            }

            return updatedOrder;
          }
          return order;
        });

        this.setData({
          orderList,
          filteredOrders: this.filterOrdersByTab(orderList, this.data.currentTab)
        });

        console.log(`订单 ${orderId} 状态更新为: ${newStatus}`);
      } catch (error) {
        console.error('更新订单状态失败:', error);
      }
    },

    /**
     * 取消订单 - 真实API调用
     * @param {string} orderId 订单ID
     * @param {string} reason 取消原因
     */
    cancelOrderAPI: function(orderId, reason = '用户主动取消') {
      console.log('取消订单:', { orderId, reason });

      return cancelOrder(orderId)
        .then((result) => {
          console.log('取消订单成功:', result);

          // 更新本地订单状态
          this.updateOrderStatusLocal(orderId, 5); // 5-已取消

          // 刷新订单列表
          this.loadOrderData();

          return result;
        })
        .catch((error) => {
          console.error('取消订单失败:', error);
          throw error;
        });
    },

    /**
     * 删除订单 - API调用预留接口
     * @param {string} orderId 订单ID
     * TODO: 替换为真实API调用
     */
    deleteOrder: function(orderId) {
      try {
        // TODO: 替换为真实API调用
        // await wx.request({
        //   url: `https://api.example.com/orders/${orderId}`,
        //   method: 'DELETE',
        //   header: {
        //     'Authorization': 'Bearer ' + wx.getStorageSync('token')
        //   }
        // });

        // 临时从本地数据中删除
        const orderList = this.data.orderList.filter(order => order.id !== orderId);

        this.setData({
          orderList,
          filteredOrders: this.filterOrdersByTab(orderList, this.data.currentTab)
        });

        console.log(`订单 ${orderId} 已删除`);
      } catch (error) {
        console.error('删除订单失败:', error);
      }
    },
    // ========== API预留接口区域结束 ==========

    // ========== 业务逻辑方法区域 ==========
    /**
     * 根据标签页筛选订单
     * @param {Array} orders 订单列表
     * @param {string} tab 标签页类型
     */
    filterOrdersByTab: function(orders, tab) {
      if (tab === 'all') {
        return orders;
      }

      const statusMapping = {
        'pending': [1],        // 待付款
        'shipped': [2, 3],     // 待收货（已支付、已发货）
        'completed': [4],      // 已完成
        'cancelled': [0, 5]    // 已取消（包含状态0和5）
      };

      const targetStatuses = statusMapping[tab] || [];
      return orders.filter(order => targetStatuses.includes(order.orderStatus));
    },

    /**
     * 切换标签页
     * @param {object} e 事件对象
     */
    onTabChange: function(e) {
      const tab = e.currentTarget.dataset.tab;
      const filteredOrders = this.filterOrdersByTab(this.data.orderList, tab);

      this.setData({
        currentTab: tab,
        filteredOrders: filteredOrders
      });

      console.log(`切换到标签页: ${tab}, 筛选后订单数量: ${filteredOrders.length}`);
    },

    /**
     * 根据ID获取订单信息
     * @param {string} orderId 订单ID
     */
    getOrderById: function(orderId) {
      return this.data.orderList.find(order => order.id === orderId);
    },

    /**
     * 获取订单状态显示文本
     * @param {string} status 订单状态
     */
    getStatusText: function(status) {
      return this.data.statusMap[status] || '未知状态';
    },

    /**
     * 计算订单商品总数量
     * @param {object} order 订单对象
     */
    getTotalQuantity: function(order) {
      return order.products.reduce((total, product) => total + product.quantity, 0);
    },

    // ========== 事件处理方法区域 ==========
    /**
     * 订单项点击事件 - 查看订单详情
     * @param {object} e 事件对象
     */
    onOrderTap: function(e) {
      const orderId = e.currentTarget.dataset.orderId;
      const order = this.getOrderById(orderId);

      if (!order) {
        wx.showToast({
          title: '订单信息不存在',
          icon: 'none'
        });
        return;
      }

      console.log('查看订单详情:', order);

      // TODO: 跳转到订单详情页面
      // wx.navigateTo({
      //   url: `/pages/order_detail/component?orderId=${orderId}`
      // });

      // 临时显示订单信息
      const statusText = this.getStatusText(order.status);
      const totalQuantity = this.getTotalQuantity(order);

      wx.showModal({
        title: '订单详情',
        content: `订单号: ${order.id}\n状态: ${statusText}\n商品数量: ${totalQuantity}件\n订单金额: ¥${order.actualAmount}\n创建时间: ${order.createTime}`,
        showCancel: false
      });
    },

    /**
     * 立即付款按钮点击事件
     * @param {object} e 事件对象
     */
    onPayNow: function(e) {
      // 兼容性处理 - 检查stopPropagation方法是否存在
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation(); // 阻止事件冒泡
      }
      
      const orderId = e.currentTarget.dataset.orderId;
      const order = this.getOrderById(orderId);

      if (!order) {
        wx.showToast({
          title: '订单信息不存在',
          icon: 'none'
        });
        return;
      }

      // 检查订单是否可以支付
      if (!canPayOrder(order)) {
        wx.showToast({
          title: '订单状态异常，无法支付',
          icon: 'none'
        });
        return;
      }

      console.log('立即付款:', order);

      // 确认支付
      wx.showModal({
        title: '确认支付',
        content: `确定要支付 ¥${order.totalAmount} 吗？`,
        confirmText: '立即支付',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.processWechatPayment(order);
          }
        }
      });
    },

    /**
     * 处理微信支付 - 真实API调用
     * @param {object} order 订单信息
     */
    processWechatPayment: function(order) {
      console.log('开始处理微信支付:', order);

      // 检查订单是否可以支付
      if (!canPayOrder(order)) {
        wx.showToast({
          title: '订单状态异常，无法支付',
          icon: 'none'
        });
        return;
      }

      return payOrderNow(order)
        .then((result) => {
          console.log('支付处理完成:', result);

          if (result.success) {
            // 支付成功，状态轮询会自动处理后续逻辑
            console.log('支付成功，等待状态确认');
          }

          return result;
        })
        .catch((error) => {
          console.error('支付处理失败:', error);
          // 错误处理已在orderHelper中完成
        });
    },

    /**
     * 支付成功回调 - 由支付状态轮询调用
     * @param {string} orderId 订单ID
     */
    onPaymentSuccess: function(orderId) {
      console.log('支付成功回调:', orderId);

      // 更新本地订单状态
      this.updateOrderStatusLocal(orderId, 2); // 2-已支付

      // 刷新订单列表
      this.loadOrderData();

      // 可选：跳转到支付成功页面
      // wx.navigateTo({
      //   url: '/pages/payment_success/component'
      // });
    },

    /**
     * 获取订单描述
     * @param {object} order 订单信息
     * @returns {string} 订单描述
     */
    getOrderDescription: function(order) {
      if (order.items && order.items.length > 0) {
        const firstItem = order.items[0];
        const productName = firstItem.productInfo ? firstItem.productInfo.productTitle : '商品';

        if (order.items.length === 1) {
          return productName;
        } else {
          return `${productName}等${order.items.length}件商品`;
        }
      }

      return '商品购买';
    },

    /**
     * 本地更新订单状态
     * @param {string} orderId 订单ID
     * @param {number} newStatus 新状态
     */
    updateOrderStatusLocal: function(orderId, newStatus) {
      const orderList = this.data.orderList.map(order => {
        if (order.id === orderId) {
          return {
            ...order,
            orderStatus: newStatus,
            statusText: this.getOrderStatusText(newStatus)
          };
        }
        return order;
      });

      this.setData({
        orderList,
        filteredOrders: this.filterOrdersByTab(orderList, this.data.currentTab)
      });
    },

    /**
     * 取消订单按钮点击事件
     * @param {object} e 事件对象
     */
    onCancelOrder: function(e) {
      // 兼容性处理 - 检查stopPropagation方法是否存在
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation(); // 阻止事件冒泡
      }
      
      const orderId = e.currentTarget.dataset.orderId;
      const order = this.getOrderById(orderId);

      if (!order) {
        wx.showToast({
          title: '订单信息不存在',
          icon: 'none'
        });
        return;
      }

      // 检查订单是否可以取消
      if (!canCancelOrder(order)) {
        wx.showToast({
          title: '该订单无法取消',
          icon: 'none'
        });
        return;
      }

      console.log('取消订单:', order);

      wx.showModal({
        title: '确认取消',
        content: '确定要取消这个订单吗？取消后无法恢复。',
        success: (res) => {
          if (res.confirm) {
            this.cancelOrderAPI(orderId, '用户主动取消');
          }
        }
      });
    },

    /**
     * 确认收货按钮点击事件
     * @param {object} e 事件对象
     */
    onConfirmReceive: function(e) {
      // 兼容性处理 - 检查stopPropagation方法是否存在
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation(); // 阻止事件冒泡
      }
      
      const orderId = e.currentTarget.dataset.orderId;
      const order = this.getOrderById(orderId);

      if (!order) {
        wx.showToast({
          title: '订单信息不存在',
          icon: 'none'
        });
        return;
      }

      if (order.orderStatus !== 3) { // 3-已发货状态才能确认收货
        wx.showToast({
          title: '订单状态异常',
          icon: 'none'
        });
        return;
      }

      console.log('确认收货:', order);

      wx.showModal({
        title: '确认收货',
        content: '确定已收到商品吗？',
        success: (res) => {
          if (res.confirm) {
            this.updateOrderStatusLocal(orderId, 4); // 4-已完成
            wx.showToast({
              title: '确认收货成功',
              icon: 'success'
            });
          }
        }
      });
    },

    /**
     * 查看物流按钮点击事件
     * @param {object} e 事件对象
     */
    onViewLogistics: function(e) {
      // 兼容性处理 - 检查stopPropagation方法是否存在
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation(); // 阻止事件冒泡
      }
      
      const orderId = e.currentTarget.dataset.orderId;
      const order = this.getOrderById(orderId);

      if (!order || !order.logistics) {
        wx.showToast({
          title: '暂无物流信息',
          icon: 'none'
        });
        return;
      }

      console.log('查看物流:', order.logistics);

      // TODO: 跳转到物流详情页面
      // wx.navigateTo({
      //   url: `/pages/logistics/component?orderId=${orderId}`
      // });

      // 临时显示物流信息
      wx.showModal({
        title: '物流信息',
        content: `快递公司: ${order.logistics.company}\n运单号: ${order.logistics.trackingNumber}\n状态: ${order.logistics.status}`,
        showCancel: false
      });
    },

    /**
     * 删除订单按钮点击事件
     * @param {object} e 事件对象
     */
    onDeleteOrder: function(e) {
      // 兼容性处理 - 检查stopPropagation方法是否存在
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation(); // 阻止事件冒泡
      }
      
      const orderId = e.currentTarget.dataset.orderId;
      const order = this.getOrderById(orderId);

      if (!order) {
        wx.showToast({
          title: '订单信息不存在',
          icon: 'none'
        });
        return;
      }

      // 只有已完成或已取消的订单才能删除
      if (![0, 4, 5].includes(order.orderStatus)) { // 0-已取消, 4-已完成, 5-已取消
        wx.showToast({
          title: '该订单无法删除',
          icon: 'none'
        });
        return;
      }

      console.log('删除订单:', order);

      wx.showModal({
        title: '确认删除',
        content: '确定要删除这个订单吗？删除后无法恢复。',
        success: (res) => {
          if (res.confirm) {
            this.deleteOrder(orderId);
            wx.showToast({
              title: '订单已删除',
              icon: 'success'
            });
          }
        }
      });
    },

    /**
     * 再次购买按钮点击事件
     * @param {object} e 事件对象
     */
    onRebuy: function(e) {
      // 兼容性处理 - 检查stopPropagation方法是否存在
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation(); // 阻止事件冒泡
      }
      
      const orderId = e.currentTarget.dataset.orderId;
      const order = this.getOrderById(orderId);

      if (!order) {
        wx.showToast({
          title: '订单信息不存在',
          icon: 'none'
        });
        return;
      }

      console.log('再次购买:', order);

      // TODO: 将商品添加到购物车并跳转
      // const productIds = order.items.map(item => item.productId);
      // wx.navigateTo({
      //   url: `/pages/cart/component?rebuy=true&products=${JSON.stringify(productIds)}`
      // });

      // 临时提示
      const itemCount = order.items ? order.items.length : 0;
      wx.showModal({
        title: '再次购买',
        content: `确定要将订单中的${itemCount}件商品加入购物车吗？`,
        success: (res) => {
          if (res.confirm) {
            wx.showToast({
              title: '已加入购物车',
              icon: 'success'
            });
            // TODO: 跳转到购物车页面
          }
        }
      });
    },

    // ========== 辅助方法区域 ==========
    /**
     * 模拟支付流程 - 临时方法
     * @param {string} orderId 订单ID
     */
    simulatePayment: function(orderId) {
      // 模拟支付成功，更新订单状态
      this.updateOrderStatus(orderId, 'paid');

      wx.showToast({
        title: '支付成功！',
        icon: 'success'
      });

      // TODO: 跳转到支付成功页面
      console.log('支付流程完成，订单ID:', orderId);
    },

    /**
     * 格式化时间显示
     * @param {string} timeString 时间字符串
     */
    formatTime: function(timeString) {
      if (!timeString) return '';

      const date = new Date(timeString);
      const now = new Date();
      const diff = now - date;

      // 一天内显示具体时间
      if (diff < 24 * 60 * 60 * 1000) {
        return timeString.split(' ')[1]; // 只显示时间部分
      }

      // 超过一天显示日期
      return timeString.split(' ')[0]; // 只显示日期部分
    },

    /**
     * 获取订单操作按钮配置
     * @param {object} order 订单对象
     */
    getOrderActions: function(order) {
      const actions = [];

      switch (order.status) {
        case 'pending':
          actions.push({ type: 'cancel', text: '取消订单' });
          actions.push({ type: 'pay', text: '立即付款', primary: true });
          break;
        case 'paid':
          actions.push({ type: 'logistics', text: '查看物流' });
          break;
        case 'shipped':
          actions.push({ type: 'logistics', text: '查看物流' });
          break;
        case 'delivered':
          actions.push({ type: 'logistics', text: '查看物流' });
          actions.push({ type: 'confirm', text: '确认收货', primary: true });
          break;
        case 'completed':
          actions.push({ type: 'delete', text: '删除订单' });
          actions.push({ type: 'rebuy', text: '再次购买', primary: true });
          break;
        case 'cancelled':
        case 'refunded':
          actions.push({ type: 'delete', text: '删除订单' });
          break;
      }

      return actions;
    }
    // ========== 业务逻辑方法区域结束 ==========
  },
});
