/**
 * 订单操作辅助工具
 * 提供取消订单和立即付款等功能
 */

// 引入API配置
const { API } = require('./api.js');
// 引入支付辅助工具
const { createPaymentAndPay, queryPaymentStatus } = require('./paymentHelper.js');

/**
 * 取消订单
 * @param {string} orderId 订单ID
 * @returns {Promise<Object>} 取消结果
 */
function cancelOrder(orderId) {
  console.log('开始取消订单:', orderId);

  // 显示加载提示
  wx.showLoading({
    title: '取消订单中...',
    mask: true
  });

  return API.order.cancel(orderId)
    .then((data) => {
      wx.hideLoading();
      console.log('取消订单API响应:', data);

      if (data.code === 200) {
        wx.showToast({
          title: '订单已取消',
          icon: 'success',
          duration: 2000
        });

        return {
          success: true,
          message: data.message || '订单取消成功',
          timestamp: data.timestamp
        };
      } else {
        throw new Error(data.message || '取消订单失败');
      }
    })
    .catch((error) => {
      wx.hideLoading();
      console.error('取消订单失败:', error);

      let errorMessage = '取消订单失败';
      if (error.message) {
        if (error.message.includes('401')) {
          errorMessage = '登录已过期，请重新登录';
        } else if (error.message.includes('403')) {
          errorMessage = '没有权限执行此操作';
        } else if (error.message.includes('404')) {
          errorMessage = '订单不存在';
        } else {
          errorMessage = error.message;
        }
      }

      wx.showModal({
        title: '取消失败',
        content: errorMessage,
        showCancel: false,
        confirmText: '确定'
      });

      throw {
        success: false,
        message: errorMessage,
        error: error
      };
    });
}

/**
 * 立即付款 - 创建支付订单并调起支付
 * @param {Object} orderInfo 订单信息
 * @param {string} orderInfo.id 订单ID
 * @param {number} orderInfo.totalAmount 订单总金额
 * @param {Array} orderInfo.items 订单商品列表
 * @returns {Promise<Object>} 支付结果
 */
function payOrderNow(orderInfo) {
  console.log('开始立即付款:', orderInfo);

  // 获取真实的用户openid
  return getRealOpenid()
    .then((openid) => {
      console.log('获取到真实openid:', openid);

      // 构建支付请求数据
      const paymentData = {
        businessOrderNo: orderInfo.id,
        paymentMethod: 1, // 1-微信支付
        totalAmount: orderInfo.totalAmount,
        productDescription: getOrderDescription(orderInfo),
        openid: openid,
        attach: JSON.stringify({
          orderId: orderInfo.id,
          orderType: 'product_order'
        }),
        remark: `订单${orderInfo.id}支付`
      };

      console.log('支付请求数据:', paymentData);

      // 调用支付辅助工具创建支付并调起支付
      return createPaymentAndPay(paymentData)
        .then((result) => {
          console.log('支付完成:', result);

          if (result.success) {
            // 支付成功后启动状态轮询
            startPaymentStatusPolling(result.paymentOrderNo, orderInfo.id);
          }

          return result;
        })
        .catch((error) => {
          console.error('支付失败:', error);
          throw error;
        });
    })
    .catch((error) => {
      console.error('获取openid失败:', error);
      
      wx.showModal({
        title: '支付失败',
        content: '获取用户信息失败，请重新登录后再试',
        showCancel: false,
        confirmText: '确定'
      });
      
      throw error;
    });
}

/**
 * 获取真实的用户openid
 * @returns {Promise<string>} openid
 */
function getRealOpenid() {
  console.log('开始获取真实openid');

  // 1. 先检查本地存储
  const storedOpenid = wx.getStorageSync('openid');
  if (storedOpenid && !storedOpenid.startsWith('mock_')) {
    console.log('使用本地存储的openid:', storedOpenid);
    return Promise.resolve(storedOpenid);
  }

  // 2. 尝试从全局App实例获取
  const app = getApp();
  if (app && app.globalData && app.globalData.openid && !app.globalData.openid.startsWith('mock_')) {
    console.log('使用全局数据的openid:', app.globalData.openid);
    wx.setStorageSync('openid', app.globalData.openid);
    return Promise.resolve(app.globalData.openid);
  }

  // 3. 调用App的getOpenid方法获取
  if (app && typeof app.getOpenid === 'function') {
    console.log('调用App.getOpenid()获取openid');
    return app.getOpenid();
  }

  // 4. 如果App方法不可用，直接调用微信登录
  console.log('直接调用微信登录获取openid');
  return new Promise((resolve, reject) => {
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          console.log('微信登录成功，code:', loginRes.code);
          
          // 调用后端API获取openid
          wx.request({
            url: 'http://localhost:8080/auth/wechat-login',
            method: 'POST',
            data: {
              code: loginRes.code
            },
            header: {
              'Content-Type': 'application/json'
            },
            success: (res) => {
              console.log('后端登录接口响应:', res);
              
              if (res.statusCode === 200 && res.data.code === 200) {
                // 从登录响应中获取用户信息，然后提取openid
                const loginData = res.data.data;
                let openid = null;
                
                // 如果返回的是用户信息对象，从中提取openid
                if (loginData && loginData.user && loginData.user.wechatOpenid) {
                  openid = loginData.user.wechatOpenid;
                } else if (loginData && loginData.wechatOpenid) {
                  openid = loginData.wechatOpenid;
                } else if (loginData && loginData.openid) {
                  openid = loginData.openid;
                }
                
                if (openid) {
                  console.log('从后端获取到openid:', openid);
                  wx.setStorageSync('openid', openid);
                  
                  // 同步到全局数据
                  if (app && app.globalData) {
                    app.globalData.openid = openid;
                  }
                  
                  resolve(openid);
                } else {
                  reject(new Error('后端返回的openid为空'));
                }
              } else {
                reject(new Error(res.data.message || '获取openid失败'));
              }
            },
            fail: (error) => {
              console.error('调用后端登录接口失败:', error);
              reject(new Error('网络请求失败，请检查网络连接'));
            }
          });
        } else {
          reject(new Error('微信登录失败'));
        }
      },
      fail: (error) => {
        console.error('微信登录调用失败:', error);
        reject(new Error('微信登录失败'));
      }
    });
  });
}

/**
 * 获取订单描述
 * @param {Object} orderInfo 订单信息
 * @returns {string} 订单描述
 */
function getOrderDescription(orderInfo) {
  if (orderInfo.items && orderInfo.items.length > 0) {
    const firstItem = orderInfo.items[0];
    let productName = '商品';

    if (firstItem.productInfo && firstItem.productInfo.productTitle) {
      productName = firstItem.productInfo.productTitle;
    } else if (firstItem.productId) {
      productName = `商品ID: ${firstItem.productId}`;
    }

    if (orderInfo.items.length === 1) {
      return productName;
    } else {
      return `${productName}等${orderInfo.items.length}件商品`;
    }
  }

  return `订单${orderInfo.id}`;
}

/**
 * 启动支付状态轮询
 * @param {string} paymentOrderNo 支付订单号
 * @param {string} businessOrderNo 业务订单号
 * @param {number} maxAttempts 最大轮询次数
 * @param {number} interval 轮询间隔（毫秒）
 */
function startPaymentStatusPolling(paymentOrderNo, businessOrderNo, maxAttempts = 10, interval = 3000) {
  console.log('开始支付状态轮询:', { paymentOrderNo, businessOrderNo, maxAttempts, interval });

  let attempts = 0;

  const pollStatus = () => {
    attempts++;
    console.log(`支付状态轮询第${attempts}次:`, paymentOrderNo);

    queryPaymentStatus(paymentOrderNo)
      .then((status) => {
        console.log('支付状态查询结果:', status);

        if (status === 1) {
          // 支付成功
          console.log('支付状态轮询完成 - 支付成功');
          wx.showToast({
            title: '支付成功',
            icon: 'success',
            duration: 2000
          });

          // 触发自定义事件通知页面刷新
          const pages = getCurrentPages();
          const currentPage = pages[pages.length - 1];
          if (currentPage && typeof currentPage.onPaymentSuccess === 'function') {
            currentPage.onPaymentSuccess(businessOrderNo);
          }

        } else if (status === 2) {
          // 支付失败
          console.log('支付状态轮询完成 - 支付失败');
          wx.showToast({
            title: '支付失败',
            icon: 'none',
            duration: 2000
          });

        } else if (status === 0 && attempts < maxAttempts) {
          // 仍在支付中，继续轮询
          console.log(`支付仍在进行中，${interval}ms后进行第${attempts + 1}次轮询`);
          setTimeout(pollStatus, interval);

        } else {
          // 超过最大轮询次数或其他状态
          console.log('支付状态轮询结束 - 超时或未知状态:', status);
          wx.showToast({
            title: '支付状态查询超时',
            icon: 'none',
            duration: 2000
          });
        }
      })
      .catch((error) => {
        console.error('支付状态查询失败:', error);

        if (attempts < maxAttempts) {
          console.log(`支付状态查询失败，${interval}ms后重试`);
          setTimeout(pollStatus, interval);
        } else {
          console.log('支付状态轮询结束 - 查询失败次数过多');
          wx.showToast({
            title: '支付状态查询失败',
            icon: 'none',
            duration: 2000
          });
        }
      });
  };

  // 延迟开始轮询，给支付一些处理时间
  setTimeout(pollStatus, 1000);
}

/**
 * 检查订单是否可以取消
 * @param {Object} orderInfo 订单信息
 * @returns {boolean} 是否可以取消
 */
function canCancelOrder(orderInfo) {
  // 只有待支付状态的订单可以取消
  return orderInfo.orderStatus === 1;
}

/**
 * 检查订单是否可以支付
 * @param {Object} orderInfo 订单信息
 * @returns {boolean} 是否可以支付
 */
function canPayOrder(orderInfo) {
  // 只有待支付状态的订单可以支付
  return orderInfo.orderStatus === 1;
}

/**
 * 获取订单状态文本
 * @param {number} status 订单状态
 * @returns {string} 状态文本
 */
function getOrderStatusText(status) {
  const statusMap = {
    0: '已取消',
    1: '待支付',
    2: '已支付',
    3: '已发货',
    4: '已完成',
    5: '已取消'
  };
  return statusMap[status] || '未知状态';
}

/**
 * 格式化订单金额
 * @param {number} amount 金额
 * @returns {string} 格式化后的金额
 */
function formatOrderAmount(amount) {
  if (typeof amount !== 'number') {
    return '¥0.00';
  }
  return `¥${amount.toFixed(2)}`;
}

/**
 * 处理订单操作错误
 * @param {Error} error 错误对象
 * @param {string} operation 操作类型
 */
function handleOrderError(error, operation) {
  console.error(`${operation}失败:`, error);

  let errorMessage = `${operation}失败`;
  if (error.message) {
    if (error.message.includes('401')) {
      errorMessage = '登录已过期，请重新登录';
      // 可以在这里处理跳转到登录页面
      wx.redirectTo({
        url: '/pages/lanhu_denglu2/component'
      });
      return;
    } else if (error.message.includes('403')) {
      errorMessage = '没有权限执行此操作';
    } else if (error.message.includes('404')) {
      errorMessage = '订单不存在';
    } else if (error.message.includes('timeout')) {
      errorMessage = '请求超时，请检查网络连接';
    } else {
      errorMessage = error.message;
    }
  }

  wx.showModal({
    title: `${operation}失败`,
    content: errorMessage,
    showCancel: false,
    confirmText: '确定'
  });
}

// 导出方法
module.exports = {
  cancelOrder,
  payOrderNow,
  startPaymentStatusPolling,
  canCancelOrder,
  canPayOrder,
  getOrderStatusText,
  formatOrderAmount,
  handleOrderError,
  getOrderDescription
};