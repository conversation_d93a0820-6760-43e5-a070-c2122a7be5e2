package com.cloudpasture.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 微信登录请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Data
@ApiModel("微信登录请求")
public class WechatLoginDTO {

    @NotBlank(message = "微信登录code不能为空")
    @ApiModelProperty(value = "微信登录code", required = true, example = "0123456789abcdef")
    private String code;
}