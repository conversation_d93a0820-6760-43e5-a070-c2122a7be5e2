/**
 * API配置文件
 * 统一管理所有API接口地址和配置
 */

// API基础配置
const API_CONFIG = {
  // 基础域名 - 请根据实际情况修改
  BASE_URL: 'http://localhost:8080',

  // 请求超时时间（毫秒）
  TIMEOUT: 10000,

  // 通用请求头
  COMMON_HEADERS: {
    'Content-Type': 'application/json'
  }
};

// API接口地址
const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/auth/login',               // 用户登录
    REGISTER: '/api/auth/register',         // 用户注册
    WECHAT_LOGIN: '/api/auth/wechat-login', // 微信登录
    BIND_WECHAT_PHONE: '/api/auth/bind-wechat-phone', // 绑定微信手机号
    REFRESH_TOKEN: '/api/auth/refresh-token', // 刷新token
    LOGOUT: '/api/auth/logout',             // 用户登出
    USER_INFO: '/api/auth/user-info'        // 获取当前用户信息
  },

  // 用户相关
  USER: {
    LIST: '/api/user/list',                 // 用户列表（管理员）
    DETAIL: '/api/user',                    // 用户详情（需要拼接ID）
    UPDATE_INFO: '/api/user/update-info',   // 更新用户信息
    POINTS: '/api/user/points',             // 获取用户积分
    ADD_POINTS: '/api/user/add-points'      // 增加用户积分（管理员）
  },

  // 商品相关
  PRODUCT: {
    LIST: '/api/product/list',              // 商品列表
    DETAIL: '/api/product',                 // 商品详情（需要拼接ID）
    CREATE: '/api/product/create',          // 创建商品
    UPDATE: '/api/product/update',          // 更新商品
    SEARCH: '/api/product/search'           // 商品搜索
  },

  // 商品规格相关
  PRODUCT_SPECIFICATION: {
    DETAIL: '/api/product-specification'    // 规格详情（需要拼接ID）
  },

  // 购物车相关
  CART: {
    LIST: '/api/cart/list',                 // 购物车列表
    ADD: '/api/cart/add',                   // 添加到购物车
    UPDATE: '/api/cart/update',             // 更新购物车
    REMOVE: '/api/cart/remove',             // 删除购物车商品
    CLEAR: '/api/cart/clear',               // 清空购物车
    COUNT: '/api/cart/count',               // 购物车商品数量
    CHECK: '/api/cart/check'                // 检查商品是否在购物车中
  },

  // 订单相关
  ORDER: {
    CREATE: '/api/order/create',            // 创建订单
    LIST: '/api/order/list',                // 订单列表
    DETAIL: '/api/order',                   // 订单详情（需要拼接ID）
    UPDATE_STATUS: '/api/order',            // 更新订单状态（需要拼接ID/status）
    ITEMS: '/api/order',                    // 订单商品明细（需要拼接订单ID/items）
    CANCEL: '/api/order'                    // 取消订单（需要拼接ID/cancel）
  },

  // 积分相关
  POINTS: {
    HISTORY: '/api/points/history'          // 积分历史记录
  },

  // 土地相关
  LAND: {
    LIST: '/api/land/list',                 // 土地列表
    DETAIL: '/api/land',                    // 土地详情（需要拼接ID）
    BY_TYPE: '/api/land/by-type',           // 按类型查询土地
    BY_OPERATOR: '/api/land/by-operator',   // 按操作员查询土地
    BY_AREA: '/api/land/by-area'            // 按面积范围查询土地
  },

  // 作物相关
  CROP: {
    LIST: '/api/crop/list',                 // 作物列表
    DETAIL: '/api/crop',                    // 作物详情（需要拼接ID）
    BY_LAND: '/api/crop/by-land',           // 按土地查询可种植作物
    SEARCH: '/api/crop/search'              // 作物搜索
  },

  // 租种相关
  RENTAL: {
    CALCULATE: '/api/rental/calculate',     // 计算租种费用
    CREATE: '/api/rental/create',           // 创建租种订单
    LIST: '/api/rental/list',               // 租种订单列表
    DETAIL: '/api/rental',                  // 租种订单详情（需要拼接ID）
    MY_RENTALS: '/api/rental/my-rentals'    // 我的租种订单
  },

  // 作物租赁相关
  CROP_RENTAL: {
    CROP_DETAIL: '/api/crop-rental/crop',   // 作物详情（需要拼接ID）
    RENTAL_INFO: '/api/crop-rental/rental', // 租赁信息（需要拼接ID）
    CREATE_ORDER: '/api/crop-rental/order', // 创建租赁订单
    PROCESS_PAYMENT: '/api/crop-rental/payment', // 处理租赁支付
    LIST: '/api/crop-rental/list',          // 租赁列表
    MY_RENTALS: '/api/crop-rental/my-rentals' // 我的租赁
  },

  // 文件相关
  FILE: {
    UPLOAD: '/api/file/upload',             // 文件上传
    UPLOAD_IMAGE: '/api/file/upload-image'  // 图片上传
  },

  // 地址相关
  ADDRESS: {
    CREATE: '/api/address/create',          // 创建地址
    UPDATE: '/api/address',                 // 更新地址（需要拼接ID）
    DELETE: '/api/address',                 // 删除地址（需要拼接ID）
    LIST: '/api/address/list',              // 获取地址列表
    DETAIL: '/api/address',                 // 获取地址详情（需要拼接ID）
    DEFAULT: '/api/address/default',        // 获取默认地址
    SET_DEFAULT: '/api/address'             // 设置默认地址（需要拼接ID/default）
  },

  // 支付相关
  PAYMENT: {
    CREATE: '/api/payment/create',          // 创建支付订单
    ORDER: '/api/payment/order',            // 查询支付订单（需要拼接订单号）
    ORDERS: '/api/payment/orders',          // 查询用户支付订单列表
    STATUS: '/api/payment/status',          // 查询支付状态（需要拼接订单号）
    REFUND: '/api/payment/refund',          // 申请退款
    CANCEL: '/api/payment/cancel'           // 取消支付订单（需要拼接订单号）
  }
};

/**
 * 获取完整的API地址
 * @param {string} endpoint 接口路径
 * @returns {string} 完整的API地址
 */
function getApiUrl(endpoint) {
  return API_CONFIG.BASE_URL + endpoint;
}

/**
 * 通用请求方法
 * @param {object} options 请求配置
 * @returns {Promise} 请求Promise
 */
function request(options) {
  return new Promise((resolve, reject) => {
    // 获取存储的token
    const token = wx.getStorageSync('token');
    
    // 构建请求头
    const headers = {
      ...API_CONFIG.COMMON_HEADERS,
      ...options.header
    };
    
    // 如果有token，添加到请求头
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    wx.request({
      url: getApiUrl(options.url),
      method: options.method || 'GET',
      data: options.data || {},
      header: headers,
      timeout: options.timeout || API_CONFIG.TIMEOUT,
      success: (response) => {
        console.log(`API请求成功 [${options.method || 'GET'}] ${options.url}:`, response);
        
        if (response.statusCode === 200) {
          resolve(response.data);
        } else if (response.statusCode === 401) {
          // token过期，清除本地存储并跳转到登录页
          wx.clearStorageSync();
          wx.redirectTo({
            url: '/pages/lanhu_denglu2/component'
          });
          reject('登录已过期，请重新登录');
        } else {
          reject(`服务器错误 (${response.statusCode})`);
        }
      },
      fail: (error) => {
        console.error(`API请求失败 [${options.method || 'GET'}] ${options.url}:`, error);
        
        if (error.errMsg) {
          if (error.errMsg.includes('timeout')) {
            reject('请求超时，请检查网络连接');
          } else if (error.errMsg.includes('fail')) {
            reject('网络连接失败，请稍后重试');
          } else {
            reject('网络请求失败，请稍后重试');
          }
        } else {
          reject('网络请求失败，请稍后重试');
        }
      }
    });
  });
}

/**
 * 便捷的API调用方法
 */
const API = {
  // 认证相关
  auth: {
    login: (phone, password) => request({
      url: API_ENDPOINTS.AUTH.LOGIN,
      method: 'POST',
      data: { phone, password }
    }),

    register: (phone, password, nickname) => request({
      url: API_ENDPOINTS.AUTH.REGISTER,
      method: 'POST',
      data: { phone, password, nickname }
    }),

    wechatLogin: (code) => request({
      url: API_ENDPOINTS.AUTH.WECHAT_LOGIN,
      method: 'POST',
      data: { code }
    }),

    bindWechatPhone: (code) => request({
      url: API_ENDPOINTS.AUTH.BIND_WECHAT_PHONE,
      method: 'POST',
      data: { code }
    }),

    refreshToken: () => request({
      url: API_ENDPOINTS.AUTH.REFRESH_TOKEN,
      method: 'POST'
    }),

    logout: () => request({
      url: API_ENDPOINTS.AUTH.LOGOUT,
      method: 'POST'
    }),

    getUserInfo: () => request({
      url: API_ENDPOINTS.AUTH.USER_INFO,
      method: 'GET'
    })
  },

  // 商品相关
  product: {
    getList: (page = 1, limit = 10, keyword = '') => {
      const params = { page, limit };
      if (keyword) {
        params.keyword = keyword;
      }
      return request({
        url: API_ENDPOINTS.PRODUCT.LIST,
        method: 'GET',
        data: params
      });
    },

    getDetail: (id) => request({
      url: `${API_ENDPOINTS.PRODUCT.DETAIL}/${id}`,
      method: 'GET'
    }),

    getById: (id) => request({
      url: `${API_ENDPOINTS.PRODUCT.DETAIL}/${id}`,
      method: 'GET'
    })
  },

  // 商品规格相关
  productSpecification: {
    getById: (id) => request({
      url: `${API_ENDPOINTS.PRODUCT_SPECIFICATION.DETAIL}/${id}`,
      method: 'GET'
    })
  },

  // 购物车相关
  cart: {
    getList: () => request({
      url: API_ENDPOINTS.CART.LIST,
      method: 'GET'
    }),

    add: (productId, quantity) => request({
      url: API_ENDPOINTS.CART.ADD,
      method: 'POST',
      data: { productId, quantity }
    }),

    update: (productId, quantity) => request({
      url: API_ENDPOINTS.CART.UPDATE,
      method: 'PUT',
      data: { productId, quantity }
    }),

    remove: (productId) => request({
      url: API_ENDPOINTS.CART.REMOVE,
      method: 'DELETE',
      data: { productId }
    }),

    clear: () => request({
      url: API_ENDPOINTS.CART.CLEAR,
      method: 'DELETE'
    }),

    getCount: () => request({
      url: API_ENDPOINTS.CART.COUNT,
      method: 'GET'
    }),

    check: (productId) => request({
      url: API_ENDPOINTS.CART.CHECK,
      method: 'GET',
      data: { productId }
    })
  },

  // 订单相关
  order: {
    create: (orderData) => request({
      url: API_ENDPOINTS.ORDER.CREATE,
      method: 'POST',
      data: orderData
    }),

    getList: (current = 1, size = 10) => request({
      url: API_ENDPOINTS.ORDER.LIST,
      method: 'GET',
      data: { current, size }
    }),

    getDetail: (id) => request({
      url: `${API_ENDPOINTS.ORDER.DETAIL}/${id}`,
      method: 'GET'
    }),

    getItems: (orderId) => request({
      url: `${API_ENDPOINTS.ORDER.ITEMS}/${orderId}/items`,
      method: 'GET'
    }),

    cancel: (orderId) => request({
      url: `${API_ENDPOINTS.ORDER.CANCEL}/${orderId}/cancel`,
      method: 'PUT'
    })
  },

  // 用户相关
  user: {
    updateInfo: (nickname, avatarUrl) => {
      const params = {};
      if (nickname !== undefined) params.nickname = nickname;
      if (avatarUrl !== undefined) params.avatarUrl = avatarUrl;

      return request({
        url: API_ENDPOINTS.USER.UPDATE_INFO,
        method: 'PUT',
        data: params
      });
    },

    getPoints: () => request({
      url: API_ENDPOINTS.USER.POINTS,
      method: 'GET'
    })
  },

  // 积分相关
  points: {
    getHistory: (current = 1, size = 10) => request({
      url: API_ENDPOINTS.POINTS.HISTORY,
      method: 'GET',
      data: { current, size }
    })
  },

  // 土地相关
  land: {
    getList: (current = 1, size = 10) => request({
      url: API_ENDPOINTS.LAND.LIST,
      method: 'GET',
      data: { current, size }
    })
  },

  // 作物相关
  crop: {
    getList: (landId = null) => request({
      url: API_ENDPOINTS.CROP.LIST,
      method: 'GET',
      data: landId ? { landId } : {}
    }),

    getDetail: (id) => request({
      url: `${API_ENDPOINTS.CROP.DETAIL}/${id}`,
      method: 'GET'
    }),

    getByLand: (landId) => request({
      url: API_ENDPOINTS.CROP.BY_LAND,
      method: 'GET',
      data: { landId }
    })
  },

  // 租种相关
  rental: {
    calculate: (landId, cropId) => request({
      url: API_ENDPOINTS.RENTAL.CALCULATE,
      method: 'POST',
      data: { landId, cropId }
    }),

    create: (rentalData) => request({
      url: API_ENDPOINTS.RENTAL.CREATE,
      method: 'POST',
      data: rentalData
    }),

    getList: (current = 1, size = 10) => request({
      url: API_ENDPOINTS.RENTAL.LIST,
      method: 'GET',
      data: { current, size }
    }),

    getDetail: (id) => request({
      url: `${API_ENDPOINTS.RENTAL.DETAIL}/${id}`,
      method: 'GET'
    }),

    getMyRentals: (current = 1, size = 10) => request({
      url: API_ENDPOINTS.RENTAL.MY_RENTALS,
      method: 'GET',
      data: { current, size }
    })
  },

  // 作物租赁相关
  cropRental: {
    getCropDetail: (cropId) => request({
      url: `${API_ENDPOINTS.CROP_RENTAL.CROP_DETAIL}/${cropId}`,
      method: 'GET'
    }),

    getRentalInfo: (rentalId) => request({
      url: `${API_ENDPOINTS.CROP_RENTAL.RENTAL_INFO}/${rentalId}`,
      method: 'GET'
    }),

    createOrder: (orderData) => request({
      url: API_ENDPOINTS.CROP_RENTAL.CREATE_ORDER,
      method: 'POST',
      data: orderData
    }),

    processPayment: (paymentData) => request({
      url: API_ENDPOINTS.CROP_RENTAL.PROCESS_PAYMENT,
      method: 'POST',
      data: paymentData
    }),

    getList: (current = 1, size = 10) => request({
      url: API_ENDPOINTS.CROP_RENTAL.LIST,
      method: 'GET',
      data: { current, size }
    }),

    getMyRentals: (current = 1, size = 10) => request({
      url: API_ENDPOINTS.CROP_RENTAL.MY_RENTALS,
      method: 'GET',
      data: { current, size }
    })
  },

  // 支付相关
  payment: {
    create: (paymentData) => request({
      url: API_ENDPOINTS.PAYMENT.CREATE,
      method: 'POST',
      data: paymentData
    }),

    getOrder: (paymentOrderNo) => request({
      url: `${API_ENDPOINTS.PAYMENT.ORDER}/${paymentOrderNo}`,
      method: 'GET'
    }),

    getOrders: (current = 1, size = 10) => request({
      url: API_ENDPOINTS.PAYMENT.ORDERS,
      method: 'GET',
      data: { current, size }
    }),

    getStatus: (paymentOrderNo) => request({
      url: `${API_ENDPOINTS.PAYMENT.STATUS}/${paymentOrderNo}`,
      method: 'GET'
    }),

    refund: (refundData) => request({
      url: API_ENDPOINTS.PAYMENT.REFUND,
      method: 'POST',
      data: refundData
    }),

    cancel: (paymentOrderNo) => request({
      url: `${API_ENDPOINTS.PAYMENT.CANCEL}/${paymentOrderNo}`,
      method: 'POST'
    })
  },

  // 文件相关
  file: {
    upload: (filePath, folder = 'files') => {
      return new Promise((resolve, reject) => {
        const token = wx.getStorageSync('token');

        wx.uploadFile({
          url: getApiUrl(API_ENDPOINTS.FILE.UPLOAD),
          filePath: filePath,
          name: 'file',
          formData: { folder },
          header: {
            'Authorization': token ? `Bearer ${token}` : ''
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data);
              if (data.code === 200) {
                resolve(data.data);
              } else {
                reject(new Error(data.message || '上传失败'));
              }
            } catch (error) {
              reject(new Error('响应解析失败'));
            }
          },
          fail: (error) => {
            reject(error);
          }
        });
      });
    },

    uploadImage: (filePath) => {
      return new Promise((resolve, reject) => {
        const token = wx.getStorageSync('token');

        wx.uploadFile({
          url: getApiUrl(API_ENDPOINTS.FILE.UPLOAD_IMAGE),
          filePath: filePath,
          name: 'file',
          header: {
            'Authorization': token ? `Bearer ${token}` : ''
          },
          success: (res) => {
            try {
              const data = JSON.parse(res.data);
              if (data.code === 200) {
                resolve(data.data);
              } else {
                reject(new Error(data.message || '上传失败'));
              }
            } catch (error) {
              reject(new Error('响应解析失败'));
            }
          },
          fail: (error) => {
            reject(error);
          }
        });
      });
    }
  },

  // 地址相关
  address: {
    create: (addressData) => request({
      url: API_ENDPOINTS.ADDRESS.CREATE,
      method: 'POST',
      data: addressData
    }),

    update: (addressId, addressData) => request({
      url: `${API_ENDPOINTS.ADDRESS.UPDATE}/${addressId}`,
      method: 'PUT',
      data: addressData
    }),

    delete: (addressId) => request({
      url: `${API_ENDPOINTS.ADDRESS.DELETE}/${addressId}`,
      method: 'DELETE'
    }),

    getList: () => request({
      url: API_ENDPOINTS.ADDRESS.LIST,
      method: 'GET'
    }),

    getDetail: (addressId) => request({
      url: `${API_ENDPOINTS.ADDRESS.DETAIL}/${addressId}`,
      method: 'GET'
    }),

    getDefault: () => request({
      url: API_ENDPOINTS.ADDRESS.DEFAULT,
      method: 'GET'
    }),

    setDefault: (addressId) => request({
      url: `${API_ENDPOINTS.ADDRESS.SET_DEFAULT}/${addressId}/default`,
      method: 'PUT'
    })
  }
};

// 导出配置和方法
module.exports = {
  API_CONFIG,
  API_ENDPOINTS,
  getApiUrl,
  request,
  API
};
