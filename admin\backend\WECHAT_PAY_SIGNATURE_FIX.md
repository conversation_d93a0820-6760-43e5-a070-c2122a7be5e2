# 微信支付API签名错误修复文档

## 问题描述

原有的微信支付实现使用了旧版本的签名算法（MD5），但调用的是微信支付v3版本的API，导致签名验证失败。微信支付v3版本使用了全新的RSA-SHA256签名算法和认证方式。

## 修复内容

### 1. 更新签名算法

#### 原有问题：
- 使用MD5签名算法
- 签名格式不符合v3版本规范
- 缺少RSA私钥签名机制

#### 修复方案：
- 实现RSA-SHA256签名算法
- 使用商户私钥进行签名
- 符合微信支付v3版本签名规范

### 2. 更新Authorization头生成

#### 新增功能：
```java
public String generateAuthorizationHeader(String method, String url, String requestBody)
```

#### 签名串格式：
```
HTTP请求方法\n
URL路径\n
时间戳\n
随机字符串\n
请求体\n
```

#### Authorization头格式：
```
WECHATPAY2-SHA256-RSA2048 mchid="商户号",nonce_str="随机串",timestamp="时间戳",serial_no="证书序列号",signature="签名值"
```

### 3. 更新小程序支付参数生成

#### 原有问题：
- 使用MD5签名
- signType设置为"MD5"

#### 修复方案：
- 使用RSA-SHA256签名
- signType设置为"RSA"
- 签名串格式符合v3版本规范

### 4. 更新回调签名验证

#### 新增功能：
- 使用微信支付平台证书验证回调签名
- 支持AES-GCM解密回调数据
- 完整的签名验证流程

### 5. 配置文件更新

#### 新增配置项：
```yaml
wechat:
  # 商户私钥（PEM格式）
  privateKey: |
    -----BEGIN PRIVATE KEY-----
    ...
    -----END PRIVATE KEY-----
  
  # 商户证书序列号
  serialNo: "证书序列号"
  
  # 微信支付平台证书（PEM格式）
  platformCertificate: |
    -----BEGIN CERTIFICATE-----
    ...
    -----END CERTIFICATE-----
```

## 核心修复代码

### 1. RSA签名实现

```java
private String signWithRSA(String data) {
    try {
        PrivateKey privateKey = getPrivateKey();
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(data.getBytes(StandardCharsets.UTF_8));
        
        byte[] signatureBytes = signature.sign();
        return Base64.getEncoder().encodeToString(signatureBytes);
    } catch (Exception e) {
        log.error("RSA签名异常: ", e);
        return null;
    }
}
```

### 2. Authorization头生成

```java
public String generateAuthorizationHeader(String method, String url, String requestBody) {
    String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
    String nonceStr = generateNonceStr();
    
    // 构建签名串
    String signatureStr = method + "\n" + getUrlPath(url) + "\n" + 
                         timestamp + "\n" + nonceStr + "\n" + requestBody + "\n";
    
    // RSA签名
    String signature = signWithRSA(signatureStr);
    
    // 构建Authorization头
    return String.format("%s mchid=\"%s\",nonce_str=\"%s\",timestamp=\"%s\",serial_no=\"%s\",signature=\"%s\"",
        SIGNATURE_TYPE, mchId, nonceStr, timestamp, getSerialNo(), signature);
}
```

### 3. 小程序支付参数生成

```java
public Map<String, String> generateMiniProgramPayParams(String prepayId) {
    String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
    String nonceStr = generateNonceStr();
    String packageValue = "prepay_id=" + prepayId;
    String signType = "RSA";

    // 生成v3版本签名
    String paySign = generateV3PaySign(appId, timeStamp, nonceStr, packageValue);

    Map<String, String> payParams = new HashMap<>();
    payParams.put("appId", appId);
    payParams.put("timeStamp", timeStamp);
    payParams.put("nonceStr", nonceStr);
    payParams.put("package", packageValue);
    payParams.put("signType", signType);
    payParams.put("paySign", paySign);

    return payParams;
}
```

## 错误处理机制

### 1. 详细日志记录

- 记录签名生成过程的每个步骤
- 记录HTTP请求和响应的详细信息
- 记录签名验证的结果

### 2. 异常处理

- 私钥解析失败处理
- 证书验证失败处理
- 网络请求失败处理
- 签名验证失败处理

### 3. 配置验证

- 检查必要配置项是否存在
- 验证私钥和证书格式
- 提供默认值和降级方案

## 测试工具

### 1. 签名测试工具

创建了 `WechatPaySignatureTest` 类，提供：
- v3版本签名生成测试
- 小程序支付参数生成测试
- 回调签名验证测试

### 2. 测试控制器

创建了 `WechatPayTestController`，提供API接口：
- `/test/wechat-pay/signature/test` - 运行签名测试
- `/test/wechat-pay/authorization/generate` - 生成Authorization头

## 部署说明

### 1. 配置更新

需要在 `application.yml` 中配置：
- 商户私钥（从微信商户平台下载）
- 商户证书序列号
- 微信支付平台证书

### 2. 证书管理

- 私钥文件需要妥善保管，不能泄露
- 定期更新平台证书
- 监控证书过期时间

### 3. 测试验证

部署后可通过测试接口验证：
1. 调用测试接口检查签名生成
2. 查看日志确认签名格式正确
3. 进行实际支付测试

## 注意事项

1. **安全性**：私钥文件必须安全存储，建议使用环境变量或加密配置
2. **兼容性**：确保所有微信支付相关功能都使用新的签名算法
3. **监控**：建议添加支付成功率监控，及时发现签名问题
4. **备份**：保留原有代码备份，以便快速回滚

## 验证清单

- [ ] 配置文件已更新商户私钥和证书
- [ ] 统一下单API调用成功
- [ ] 小程序支付参数生成正确
- [ ] 支付回调签名验证通过
- [ ] 订单查询API调用成功
- [ ] 退款API调用成功
- [ ] 测试接口返回正常结果
- [ ] 生产环境支付流程测试通过

## 相关文档

- [微信支付API v3签名指南](https://pay.weixin.qq.com/wiki/doc/apiv3/wechatpay/wechatpay4_0.shtml)
- [微信支付商户证书使用指南](https://pay.weixin.qq.com/wiki/doc/apiv3/wechatpay/wechatpay7_0.shtml)
- [微信支付回调通知指南](https://pay.weixin.qq.com/wiki/doc/apiv3/wechatpay/wechatpay4_2.shtml)