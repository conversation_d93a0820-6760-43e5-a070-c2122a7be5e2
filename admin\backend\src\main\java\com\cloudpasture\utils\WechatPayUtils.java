package com.cloudpasture.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cloudpasture.dto.WechatPayNotifyDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 微信支付工具类
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Slf4j
@Component
public class WechatPayUtils {

    @Value("${wechat.appId}")
    private String appId;

    @Value("${wechat.mchId}")
    private String mchId;

    @Value("${wechat.apiKey}")
    private String apiKey;

    @Value("${wechat.notifyUrl}")
    private String notifyUrl;

    @Value("${wechat.privateKey:}")
    private String privateKeyString;

    @Value("${wechat.serialNo:}")
    private String serialNo;

    @Value("${wechat.platformCertificate:}")
    private String platformCertificate;

    // 签名类型
    private static final String SIGNATURE_TYPE = "WECHATPAY2-SHA256-RSA2048";

    // 微信支付API地址
    private static final String WECHAT_PAY_URL = "https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi";
    private static final String WECHAT_QUERY_URL = "https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no/";
    private static final String WECHAT_REFUND_URL = "https://api.mch.weixin.qq.com/v3/refund/domestic/refunds";

    /**
     * 创建微信支付订单（统一下单）
     * 
     * @param outTradeNo 商户订单号
     * @param totalAmount 支付金额（分）
     * @param description 商品描述
     * @param openid 用户openid
     * @param attach 附加数据
     * @return 预支付交易会话标识
     */
    public String createPayOrder(String outTradeNo, Integer totalAmount, String description, String openid, String attach) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("appid", appId);
            params.put("mchid", mchId);
            params.put("description", description);
            params.put("out_trade_no", outTradeNo);
            params.put("notify_url", notifyUrl);
            if (attach != null) {
                params.put("attach", attach);
            }

            // 金额信息
            Map<String, Object> amount = new HashMap<>();
            amount.put("total", totalAmount);
            amount.put("currency", "CNY");
            params.put("amount", amount);

            // 支付者信息
            Map<String, Object> payer = new HashMap<>();
            payer.put("openid", openid);
            params.put("payer", payer);

            String requestBody = JSON.toJSONString(params);
            log.info("微信支付统一下单请求参数: {}", requestBody);

            // 发送请求
            String response = sendPostRequest(WECHAT_PAY_URL, requestBody);
            log.info("微信支付统一下单响应: {}", response);

            JSONObject responseJson = JSON.parseObject(response);
            if (responseJson.containsKey("prepay_id")) {
                return responseJson.getString("prepay_id");
            } else {
                log.error("微信支付统一下单失败: {}", response);
                return null;
            }

        } catch (Exception e) {
            log.error("微信支付统一下单异常: ", e);
            return null;
        }
    }

    /**
     * 查询支付订单
     * 
     * @param outTradeNo 商户订单号
     * @return 订单信息
     */
    public JSONObject queryPayOrder(String outTradeNo) {
        try {
            String url = WECHAT_QUERY_URL + outTradeNo + "?mchid=" + mchId;
            String response = sendGetRequest(url);
            log.info("微信支付订单查询响应: {}", response);

            return JSON.parseObject(response);

        } catch (Exception e) {
            log.error("微信支付订单查询异常: ", e);
            return null;
        }
    }

    /**
     * 申请退款
     * 
     * @param outTradeNo 商户订单号
     * @param outRefundNo 商户退款单号
     * @param totalAmount 原订单金额（分）
     * @param refundAmount 退款金额（分）
     * @param reason 退款原因
     * @return 退款结果
     */
    public JSONObject refund(String outTradeNo, String outRefundNo, Integer totalAmount, Integer refundAmount, String reason) {
        try {
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("out_trade_no", outTradeNo);
            params.put("out_refund_no", outRefundNo);
            params.put("reason", reason);

            // 金额信息
            Map<String, Object> amount = new HashMap<>();
            amount.put("refund", refundAmount);
            amount.put("total", totalAmount);
            amount.put("currency", "CNY");
            params.put("amount", amount);

            String requestBody = JSON.toJSONString(params);
            log.info("微信支付退款请求参数: {}", requestBody);

            // 发送请求
            String response = sendPostRequest(WECHAT_REFUND_URL, requestBody);
            log.info("微信支付退款响应: {}", response);

            return JSON.parseObject(response);

        } catch (Exception e) {
            log.error("微信支付退款异常: ", e);
            return null;
        }
    }

    /**
     * 生成小程序支付参数
     * 
     * @param prepayId 预支付交易会话标识
     * @return 小程序支付参数
     */
    public Map<String, String> generateMiniProgramPayParams(String prepayId) {
        try {
            String timeStamp = String.valueOf(System.currentTimeMillis() / 1000);
            String nonceStr = generateNonceStr();
            String packageValue = "prepay_id=" + prepayId;
            String signType = "RSA";

            // 生成v3版本签名
            String paySign = generateV3PaySign(appId, timeStamp, nonceStr, packageValue);

            Map<String, String> payParams = new HashMap<>();
            payParams.put("appId", appId);
            payParams.put("timeStamp", timeStamp);
            payParams.put("nonceStr", nonceStr);
            payParams.put("package", packageValue);
            payParams.put("signType", signType);
            payParams.put("paySign", paySign);

            log.info("生成小程序支付参数成功: appId={}, timeStamp={}, nonceStr={}, package={}", 
                appId, timeStamp, nonceStr, packageValue);

            return payParams;

        } catch (Exception e) {
            log.error("生成小程序支付参数异常: ", e);
            return null;
        }
    }

    /**
     * 验证支付回调签名
     * 
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param body 请求体
     * @param signature 签名
     * @return 验证结果
     */
    public boolean verifyNotifySignature(String timestamp, String nonce, String body, String signature) {
        try {
            String data = timestamp + "\n" + nonce + "\n" + body + "\n";
            String expectedSignature = generateSignature(data);
            return expectedSignature.equals(signature);

        } catch (Exception e) {
            log.error("验证支付回调签名异常: ", e);
            return false;
        }
    }

    /**
     * 解密支付回调数据
     * 
     * @param ciphertext 密文
     * @param associatedData 附加数据
     * @param nonce 随机串
     * @return 解密后的数据
     */
    public String decryptNotifyData(String ciphertext, String associatedData, String nonce) {
        try {
            byte[] key = apiKey.getBytes(StandardCharsets.UTF_8);
            byte[] nonceBytes = nonce.getBytes(StandardCharsets.UTF_8);
            byte[] ciphertextBytes = Base64.getDecoder().decode(ciphertext);
            byte[] associatedDataBytes = associatedData.getBytes(StandardCharsets.UTF_8);

            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            SecretKeySpec secretKeySpec = new SecretKeySpec(key, "AES");
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(128, nonceBytes);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, gcmParameterSpec);
            cipher.updateAAD(associatedDataBytes);

            byte[] decryptedBytes = cipher.doFinal(ciphertextBytes);
            return new String(decryptedBytes, StandardCharsets.UTF_8);

        } catch (Exception e) {
            log.error("解密支付回调数据异常: ", e);
            return null;
        }
    }

    /**
     * 生成随机字符串
     */
    private String generateNonceStr() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 生成签名（兼容旧版本）
     */
    private String generateSignature(String data) {
        try {
            return signWithRSA(data);
        } catch (Exception e) {
            log.error("生成签名异常: ", e);
            return null;
        }
    }

    /**
     * 生成微信支付v3版本小程序支付签名
     */
    private String generateV3PaySign(String appId, String timeStamp, String nonceStr, String packageValue) {
        try {
            // 构建签名串
            String signatureStr = appId + "\n" + timeStamp + "\n" + nonceStr + "\n" + packageValue + "\n";
            
            log.info("小程序支付签名串: {}", signatureStr);
            
            // 使用商户私钥进行RSA-SHA256签名
            return signWithRSA(signatureStr);

        } catch (Exception e) {
            log.error("生成v3版本小程序支付签名异常: ", e);
            return null;
        }
    }

    /**
     * 生成微信支付v3版本API请求Authorization头
     */
    public String generateAuthorizationHeader(String method, String url, String requestBody) {
        try {
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
            String nonceStr = generateNonceStr();
            
            // 构建签名串
            String signatureStr = method + "\n" + getUrlPath(url) + "\n" + timestamp + "\n" + nonceStr + "\n" + requestBody + "\n";
            
            log.info("API请求签名串: {}", signatureStr);
            log.info("请求方法: {}, URL路径: {}, 时间戳: {}, 随机串: {}", method, getUrlPath(url), timestamp, nonceStr);
            
            // 使用商户私钥进行RSA-SHA256签名
            String signature = signWithRSA(signatureStr);
            
            if (signature == null) {
                log.error("生成签名失败");
                return null;
            }
            
            // 构建Authorization头
            String authorization = String.format("%s mchid=\"%s\",nonce_str=\"%s\",timestamp=\"%s\",serial_no=\"%s\",signature=\"%s\"",
                SIGNATURE_TYPE, mchId, nonceStr, timestamp, getSerialNo(), signature);
            
            log.info("生成Authorization头: {}", authorization);
            
            return authorization;

        } catch (Exception e) {
            log.error("生成Authorization头异常: ", e);
            return null;
        }
    }

    /**
     * 使用RSA私钥进行SHA256签名
     */
    private String signWithRSA(String data) {
        try {
            PrivateKey privateKey = getPrivateKey();
            if (privateKey == null) {
                log.error("获取私钥失败");
                return null;
            }
            
            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initSign(privateKey);
            signature.update(data.getBytes(StandardCharsets.UTF_8));
            
            byte[] signatureBytes = signature.sign();
            String result = Base64.getEncoder().encodeToString(signatureBytes);
            
            log.info("RSA签名成功，签名长度: {}", result.length());
            
            return result;

        } catch (Exception e) {
            log.error("RSA签名异常: ", e);
            return null;
        }
    }

    /**
     * 获取商户私钥
     */
    private PrivateKey getPrivateKey() {
        try {
            if (privateKeyString == null || privateKeyString.trim().isEmpty()) {
                log.warn("商户私钥未配置，使用模拟私钥");
                // 返回null，让调用方处理
                return null;
            }
            
            // 移除私钥字符串中的头尾标识和换行符
            String privateKeyPEM = privateKeyString
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", "");
            
            byte[] keyBytes = Base64.getDecoder().decode(privateKeyPEM);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            
            return keyFactory.generatePrivate(keySpec);

        } catch (Exception e) {
            log.error("解析商户私钥异常: ", e);
            return null;
        }
    }

    /**
     * 获取商户证书序列号
     */
    private String getSerialNo() {
        if (serialNo != null && !serialNo.trim().isEmpty()) {
            return serialNo;
        }
        
        // 如果没有配置序列号，返回默认值
        log.warn("商户证书序列号未配置，使用默认值");
        return "DEFAULT_SERIAL_NO";
    }

    /**
     * 从URL中提取路径部分
     */
    private String getUrlPath(String url) {
        try {
            if (url.startsWith("http://") || url.startsWith("https://")) {
                int index = url.indexOf("/", 8); // 跳过协议部分
                return index > 0 ? url.substring(index) : "/";
            }
            return url.startsWith("/") ? url : "/" + url;
        } catch (Exception e) {
            log.error("解析URL路径异常: ", e);
            return "/";
        }
    }


    /**
     * 使用微信支付平台证书验证签名
     */
    private boolean verifySignatureWithPlatformCert(String data, String signature) {
        try {
            if (platformCertificate == null || platformCertificate.trim().isEmpty()) {
                log.warn("微信支付平台证书未配置，跳过签名验证");
                return true; // 开发环境可以跳过验证
            }
            
            // 解析平台证书
            X509Certificate cert = parseCertificate(platformCertificate);
            if (cert == null) {
                log.error("解析平台证书失败");
                return false;
            }
            
            // 验证签名
            Signature sig = Signature.getInstance("SHA256withRSA");
            sig.initVerify(cert.getPublicKey());
            sig.update(data.getBytes(StandardCharsets.UTF_8));
            
            byte[] signatureBytes = Base64.getDecoder().decode(signature);
            boolean result = sig.verify(signatureBytes);
            
            log.info("平台证书验签结果: {}", result);
            
            return result;

        } catch (Exception e) {
            log.error("使用平台证书验证签名异常: ", e);
            return false;
        }
    }

    /**
     * 解析X509证书
     */
    private X509Certificate parseCertificate(String certificateStr) {
        try {
            // 移除证书字符串中的头尾标识和换行符
            String certPEM = certificateStr
                .replace("-----BEGIN CERTIFICATE-----", "")
                .replace("-----END CERTIFICATE-----", "")
                .replaceAll("\\s", "");
            
            byte[] certBytes = Base64.getDecoder().decode(certPEM);
            InputStream certStream = new ByteArrayInputStream(certBytes);
            
            CertificateFactory certFactory = CertificateFactory.getInstance("X.509");
            return (X509Certificate) certFactory.generateCertificate(certStream);

        } catch (Exception e) {
            log.error("解析X509证书异常: ", e);
            return null;
        }
    }

    /**
     * 发送POST请求
     */
    private String sendPostRequest(String url, String requestBody) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        
        // 设置请求头
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("Accept", "application/json");
        httpPost.setHeader("User-Agent", "CloudPasture-WechatPay/1.0");
        
        // 生成并设置Authorization头
        String authorization = generateAuthorizationHeader("POST", url, requestBody);
        if (authorization != null) {
            httpPost.setHeader("Authorization", authorization);
            log.info("设置Authorization头: {}", authorization);
        } else {
            log.warn("生成Authorization头失败，可能影响API调用");
        }
        
        httpPost.setEntity(new StringEntity(requestBody, StandardCharsets.UTF_8));
        
        log.info("发送POST请求到: {}", url);
        log.info("请求体: {}", requestBody);
        
        CloseableHttpResponse response = httpClient.execute(httpPost);
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        
        log.info("响应状态码: {}", statusCode);
        log.info("响应体: {}", responseBody);
        
        httpClient.close();
        
        if (statusCode != 200) {
            log.error("微信支付API调用失败，状态码: {}, 响应: {}", statusCode, responseBody);
            throw new Exception("微信支付API调用失败，状态码: " + statusCode);
        }
        
        return responseBody;
    }

    /**
     * 发送GET请求
     */
    private String sendGetRequest(String url) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet httpGet = new HttpGet(url);
        
        // 设置请求头
        httpGet.setHeader("Accept", "application/json");
        httpGet.setHeader("User-Agent", "CloudPasture-WechatPay/1.0");
        
        // 生成并设置Authorization头
        String authorization = generateAuthorizationHeader("GET", url, "");
        if (authorization != null) {
            httpGet.setHeader("Authorization", authorization);
            log.info("设置Authorization头: {}", authorization);
        } else {
            log.warn("生成Authorization头失败，可能影响API调用");
        }
        
        log.info("发送GET请求到: {}", url);
        
        CloseableHttpResponse response = httpClient.execute(httpGet);
        int statusCode = response.getStatusLine().getStatusCode();
        String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
        
        log.info("响应状态码: {}", statusCode);
        log.info("响应体: {}", responseBody);
        
        httpClient.close();
        
        if (statusCode != 200) {
            log.error("微信支付API调用失败，状态码: {}, 响应: {}", statusCode, responseBody);
            throw new Exception("微信支付API调用失败，状态码: " + statusCode);
        }
        
        return responseBody;
    }
}
