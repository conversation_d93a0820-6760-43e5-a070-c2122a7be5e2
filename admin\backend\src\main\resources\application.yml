server:
  port: 8080
  servlet:
    context-path: /api

spring:
  profiles:
    active: default
  application:
    name: cloud-pasture-mall
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *****************************************************************************************************************************************************************************************
    username: root
    password: xiaoma
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  # 解决 Springfox 与 Spring Boot 2.6+ 兼容性问题
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml

# 日志配置
logging:
  level:
    com.cloudpasture.mapper: debug
    root: info
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'

# JWT配置
jwt:
  secret: cloudpasture2025
  expiration: 604800

# 微信小程序配置（已是生产环境版本）
wechat:
  miniapp:
    app-id: wxa179c4885f1074a2 
    app-secret: 056cf1108a80a4fa2a9ae2ab57c41958  
  # 微信支付配置 
  appId: wxa179c4885f1074a2  
  mchId: 1724600487  
  apiKey: AbCd1234EfGh5678IjKl9012MnOp3456  
  notifyUrl: https://localhost:8080/api/payment/wechat/notify  
  
  # 微信支付v3版本配置 已是生产环境配置
  privateKey: |
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  
  serialNo: "1F12E6BD077A966574916200978CD7931DD47033" 
  
  platformCertificate: |
    -----BEGIN CERTIFICATE-----
    MIIELjCCAxagAwIBAgIUHxLmvQd6lmV0kWIAl4zXkx3UcDMwDQYJKoZIhvcNAQEL
    BQAwXjELMAkGA1UEBhMCQ04xEzARBgNVBAoTClRlbnBheS5jb20xHTAbBgNVBAsT
    FFRlbnBheS5jb20gQ0EgQ2VudGVyMRswGQYDVQQDExJUZW5wYXkuY29tIFJvb3Qg
    Q0EwHhcNMjUwODEwMDQ0NzQwWhcNMzAwODA5MDQ0NzQwWjCBhzETMBEGA1UEAwwK
    MTcyNDYwMDQ4NzEbMBkGA1UECgwS5b6u5L+h5ZWG5oi357O757ufMTMwMQYDVQQL
    DCrmsZ/oi4/mmI7otKToh6rnhLbnlJ/mgIHlhpzlnLrmnInpmZDlhazlj7gxCzAJ
    BgNVBAYTAkNOMREwDwYDVQQHDAhTaGVuWmhlbjCCASIwDQYJKoZIhvcNAQEBBQAD
    ggEPADCCAQoCggEBAKfokYMPG6EqPvycx+k+i3UF8YjU7TVqq38vwZdj03DkDhSW
    3XzFeyJCNTR0iV/lVQvDVoISdvd0JFpcWChhHG9/JYC4oxQOUvFbGrHfHLhmMsmc
    LpjRr6UEBIng50uGJmWL3jqh20DjXS7iBJZgeZC8Ce/Y1y/l2wkfGPjxf36gL9bS
    lmfJTw8g/AEO8/alxfenWPu4FpYbRGDk7F20IEz1F8tAtjF1AgqN8dXxAx0D6MTt
    wt8gIJqH3kFY4OqQ6rLDPYANDZE51EYzrx5BwMBl2sJN6BIvUdpkAm0jFxsAG1l6
    DjT+lO8jSp9rGKm1dl8j2zh69ioCGMWt2j0QOaMCAwEAAaOBuTCBtjAJBgNVHRME
    AjAAMAsGA1UdDwQEAwID+DCBmwYDVR0fBIGTMIGQMIGNoIGKoIGHhoGEaHR0cDov
    L2V2Y2EuaXRydXMuY29tLmNuL3B1YmxpYy9pdHJ1c2NybD9DQT0xQkQ0MjIwRTUw
    REJDMDRCMDZBRDM5NzU0OTg0NkMwMUMzRThFQkQyJnNnPUhBQ0M0NzFCNjU0MjJF
    MTJCMjdBOUQzM0E4N0FEMUNERjU5MjZFMTQwMzcxMA0GCSqGSIb3DQEBCwUAA4IB
    AQCR/Em9mbXHxnu/8oDL5n7L9pfMQdL642V8Wa0G5Fx7ZH4khOpi2E377XikxU7a
    0X4tfAo/hvHCjEzElKvcqmLDYshcNYjPAkvr0leXh3U3fOs5Pfc08ElDSxyt6919
    Fy37r9MDRbsOzlVZviunfH/rA964Sny0Auy8ESmihdzs56Bhj1Lwqld0XOQgCZzG
    KKOl9/+k6W19mxtUdJef9TZe2QHs498trb0skguZrXBmvSRevblJhTLQGD2PfHCI
    08Tiz5u4tN/QL5dQLCoJ8OqhVZDNw5qt75Fr54JWLdE46HVpF7mj7+5uhbu8+8uN
    NJBEt8rMcdYjE0jvY0vuN7OY
    -----END CERTIFICATE-----

# 腾讯云COS配置
cos:
  secret-id: AKIDGNHxPddR2PPQEqEOL5btzvfBUZR6XkkX
  secret-key: sb9g3DA0rbLwLYlT0KQOMCtUuAQWO3bP
  region: ap-guangzhou
  bucket-name: yutingnongchang-1369375721
  base-url: https://yutingnongchang-1369375721.cos.ap-guangzhou.myqcloud.com
  # CDN加速域名
  cdn-domain: https://cos.uuutoo.cn

# Knife4j配置
knife4j:
  enable: true
  openapi:
    title: 愉汀农场小程序API文档
    description: 愉汀农场小程序后端API接口文档
    version: 1.0.0
    concat: 开发团队
  setting:
    language: zh_cn
    enable-version: true
    enable-swagger-models: true
    enable-reload-cache-parameter: true
    enable-after-script: true
    enable-filter-multipart-api-method-type: POST
    enable-filter-multipart-apis: false
    enable-request-cache: true
    enable-host: false
    enable-host-text: localhost:8080
    enable-home-custom: true
    home-custom-path: classpath:markdown/home.md
    enable-search: true
    enable-footer: false
    enable-footer-custom: true
    footer-custom-content: Apache License 2.0 | Copyright 2025-[愉汀农场小程序]
