.page {
  background-color: #f5f5f5;
}

.container {
  width: 750rpx;
  background-color: #f5f5f5;
}

/* 订单列表容器样式 */
.order-list {
  margin-top: 20rpx;
  padding: 20rpx;
  width: 750rpx;
  box-sizing: border-box;
}

/* 标签页导航样式 */
.tab-bar {
  display: flex;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
  width: 750rpx;
  margin-top:-230rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  position: relative;
}

.tab-item.active {
  color: #ff6b35;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #ff6b35;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 30rpx;
  color: #666666;
}

.tab-item.active .tab-text {
  color: #ff6b35;
  font-weight: bold;
}

/* 订单列表样式 */

.order-item {
  background-color: #ffffff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f0f0f0;
}

.order-number {
  font-size: 28rpx;
  color: #666666;
}

.order-status {
  font-size: 28rpx;
  font-weight: bold;
}

/* 订单状态颜色 */
.status-0 {
  color: #999999;
}

.status-1 {
  color: #ff9500;
}

.status-2 {
  color: #007aff;
}

.status-3 {
  color: #007aff;
}

.status-4 {
  color: #34c759;
}

.status-5 {
  color: #999999;
}

/* 兼容旧的状态样式 */
.status-pending {
  color: #ff9500;
}

.status-paid {
  color: #007aff;
}

.status-shipped {
  color: #007aff;
}

.status-delivered {
  color: #007aff;
}

.status-completed {
  color: #34c759;
}

.status-cancelled {
  color: #999999;
}

.status-refunded {
  color: #999999;
}

.order-content {
  margin-bottom: 20rpx;
}

.product-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.product-item:last-child {
  margin-bottom: 0;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 10rpx;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-spec {
  font-size: 26rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.product-price-quantity {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

.product-quantity {
  font-size: 26rpx;
  color: #999999;
}

/* 订单金额样式 */
.order-amount {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 15rpx;
  padding-top: 15rpx;
  border-top: 1px solid #f0f0f0;
}

.amount-label {
  font-size: 28rpx;
  color: #666666;
  margin-right: 10rpx;
}

.amount-value {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 订单时间样式 */
.order-time {
  margin-bottom: 20rpx;
}

.time-text {
  font-size: 26rpx;
  color: #999999;
}

/* 订单操作按钮样式 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 20rpx;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  border: 1px solid #e0e0e0;
  background-color: #ffffff;
  min-width: 140rpx;
  text-align: center;
}

.action-btn.primary-btn {
  background-color: #ff6b35;
  border-color: #ff6b35;
}

.action-btn.cancel-btn {
  border-color: #ff4757;
}

.btn-text {
  font-size: 28rpx;
  color: #333333;
}

.primary-btn .btn-text {
  color: #ffffff;
  font-weight: bold;
}

.cancel-btn .btn-text {
  color: #ff4757;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  border-radius: 50%;
}

.empty-icon-inner {
  font-size: 80rpx;
  color: #cccccc;
}

.empty-text {
  font-size: 32rpx;
  color: #666666;
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #999999;
  line-height: 1.5;
}

/* 响应式适配 */
@media screen and (max-width: 375px) {
  .tab-text {
    font-size: 28rpx;
  }

  .product-name {
    font-size: 28rpx;
  }

  .product-price {
    font-size: 30rpx;
  }

  .amount-value {
    font-size: 30rpx;
  }
}

/* 动画效果 */
.order-item {
  transition: transform 0.2s ease;
}

.order-item:active {
  transform: scale(0.98);
}

.action-btn {
  transition: all 0.2s ease;
}

.action-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

.tab-item {
  transition: color 0.2s ease;
}
