<view class="page">
  <view class="container">
    <!-- 标签页导航 -->
    <view class="tab-bar">
      <view class="tab-item {{currentTab === 'all' ? 'active' : ''}}" bindtap="onTabChange" data-tab="all">
        <text class="tab-text">全部</text>
      </view>
      <view class="tab-item {{currentTab === 'pending' ? 'active' : ''}}" bindtap="onTabChange" data-tab="pending">
        <text class="tab-text">待付款</text>
      </view>
      <view class="tab-item {{currentTab === 'shipped' ? 'active' : ''}}" bindtap="onTabChange" data-tab="shipped">
        <text class="tab-text">待收货</text>
      </view>
      <view class="tab-item {{currentTab === 'completed' ? 'active' : ''}}" bindtap="onTabChange" data-tab="completed">
        <text class="tab-text">已完成</text>
      </view>
      <view class="tab-item {{currentTab === 'cancelled' ? 'active' : ''}}" bindtap="onTabChange" data-tab="cancelled">
        <text class="tab-text">已取消</text>
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list">
      <block wx:for="{{filteredOrders}}" wx:key="id" wx:for-item="order">
        <view class="order-item" bindtap="onOrderTap" data-order-id="{{order.id}}">
          <!-- 订单头部信息 -->
          <view class="order-header">
            <text class="order-number">订单号：{{order.id}}</text>
            <text class="order-status status-{{order.orderStatus}}">{{order.statusText}}</text>
          </view>

          <!-- 订单商品列表 -->
          <view class="order-content">
            <block wx:for="{{order.items}}" wx:key="id" wx:for-item="item">
              <view class="product-item">
                <image class="product-image" src="{{item.productImageUrl || '/images/default-product.png'}}" mode="aspectFill"></image>
                <view class="product-info">
                  <text class="product-name">{{item.productInfo.productTitle || '商品ID: ' + item.productId}}</text>
                  <text class="product-spec" wx:if="{{item.specificationInfo}}">规格：{{item.specificationInfo.specificationName}}</text>
                  <view class="product-price-quantity">
                    <text class="product-price">￥{{item.productPrice}}</text>
                    <text class="product-quantity">x{{item.productQuantity}}</text>
                  </view>
                </view>
              </view>
            </block>
          </view>

          <!-- 订单金额信息 -->
          <view class="order-amount">
            <text class="amount-label">实付金额：</text>
            <text class="amount-value">￥{{order.totalAmount}}</text>
          </view>

          <!-- 订单时间信息 -->
          <view class="order-time">
            <text class="time-text">下单时间：{{order.createTime}}</text>
          </view>

          <!-- 订单操作按钮 -->
          <view class="order-actions">
            <block wx:if="{{order.orderStatus === 1}}">
              <view class="action-btn cancel-btn" bindtap="onCancelOrder" data-order-id="{{order.id}}">
                <text class="btn-text">取消订单</text>
              </view>
              <view class="action-btn primary-btn" bindtap="onPayNow" data-order-id="{{order.id}}">
                <text class="btn-text">立即付款</text>
              </view>
            </block>

            <block wx:elif="{{order.orderStatus === 2 || order.orderStatus === 3}}">
              <view class="action-btn" bindtap="onViewLogistics" data-order-id="{{order.id}}">
                <text class="btn-text">查看物流</text>
              </view>
            </block>

            <block wx:elif="{{order.orderStatus === 4}}">
              <view class="action-btn" bindtap="onDeleteOrder" data-order-id="{{order.id}}">
                <text class="btn-text">删除订单</text>
              </view>
              <view class="action-btn primary-btn" bindtap="onRebuy" data-order-id="{{order.id}}">
                <text class="btn-text">再次购买</text>
              </view>
            </block>

            <block wx:elif="{{order.orderStatus === 5 || order.orderStatus === 0}}">
              <view class="action-btn" bindtap="onDeleteOrder" data-order-id="{{order.id}}">
                <text class="btn-text">删除订单</text>
              </view>
            </block>
          </view>
        </view>
      </block>

      <!-- 空状态提示 -->
      <view wx:if="{{filteredOrders.length === 0}}" class="empty-state">
        <view class="empty-icon">
          <view class="empty-icon-inner">📋</view>
        </view>
        <text class="empty-text">暂无相关订单</text>
        <text class="empty-desc">快去选购心仪的商品吧~</text>
      </view>
    </view>
  </view>
</view>
