package com.cloudpasture.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudpasture.common.BusinessException;
import com.cloudpasture.common.ResultCode;
import com.cloudpasture.entity.Points;
import com.cloudpasture.entity.User;
import com.cloudpasture.mapper.PointsMapper;
import com.cloudpasture.service.PointsService;
import com.cloudpasture.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 积分变动记录表 服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-19
 */
@Slf4j
@Service
public class PointsServiceImpl extends ServiceImpl<PointsMapper, Points> implements PointsService {

    @Autowired
    private UserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPoints(Long userId, Integer points, String reason, String relatedOrderNo) {
        if (points <= 0) {
            throw new BusinessException("积分数量必须大于0");
        }

        // 创建积分记录
        Points pointsRecord = new Points();
        pointsRecord.setUserId(userId);
        pointsRecord.setChangeAmount(points);
        pointsRecord.setRelatedOrderNo(relatedOrderNo);
        pointsRecord.setCreateTime(LocalDateTime.now());

        boolean success = save(pointsRecord);
        if (success) {
            // 更新用户积分
            userService.updateUserPoints(userId, points);
            log.info("用户积分增加成功，用户ID：{}，积分：{}，原因：{}，订单号：{}", userId, points, reason, relatedOrderNo);
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deductPoints(Long userId, Integer points, String reason, String relatedOrderNo) {
        if (points <= 0) {
            throw new BusinessException("积分数量必须大于0");
        }

        // 检查积分是否充足
        if (!checkPointsSufficient(userId, points)) {
            throw new BusinessException(ResultCode.POINTS_INSUFFICIENT);
        }

        // 创建积分记录（负数表示扣减）
        Points pointsRecord = new Points();
        pointsRecord.setUserId(userId);
        pointsRecord.setChangeAmount(-points);
        pointsRecord.setRelatedOrderNo(relatedOrderNo);
        pointsRecord.setCreateTime(LocalDateTime.now());

        boolean success = save(pointsRecord);
        if (success) {
            // 更新用户积分
            userService.updateUserPoints(userId, -points);
            log.info("用户积分扣减成功，用户ID：{}，积分：{}，原因：{}，订单号：{}", userId, points, reason, relatedOrderNo);
        }
        return success;
    }

    @Override
    public IPage<Points> getUserPointsHistory(Long userId, Page<Points> page) {
        return baseMapper.selectPageByUserId(page, userId);
    }

    @Override
    public Integer calculateUserTotalPoints(Long userId) {
        Integer totalPoints = baseMapper.sumPointsByUserId(userId);
        return totalPoints != null ? totalPoints : 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncUserPoints(Long userId) {
        Integer totalPoints = calculateUserTotalPoints(userId);
        
        User user = userService.getById(userId);
        if (user != null) {
            user.setPoints(totalPoints);
            boolean success = userService.updateById(user);
            if (success) {
                log.info("用户积分同步成功，用户ID：{}，总积分：{}", userId, totalPoints);
            }
            return success;
        }
        return false;
    }

    @Override
    public boolean checkPointsSufficient(Long userId, Integer points) {
        Integer userPoints = userService.getUserPoints(userId);
        return userPoints >= points;
    }
}
